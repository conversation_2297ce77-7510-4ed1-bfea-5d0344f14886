import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import taskApi from '@/service/taskApi';
import { message, Button, Spin, Input, Tabs, Table, Space, Modal } from 'antd';
import { Token } from '@/Constant';
import baseApi from '@/service/baseApi';
import dicParams from '@/pages/InspectionPlan/dicParams';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';

import { validateMobileOrLinePhone } from '@/utils/customMethod';
import { BaseResponse } from '@/types/common';
import { TaskVo, TaskInsertParam, TaskUpdateParam, TaskDeviceVo, TaskPointsVo } from '@/types/task';
import { IYTHColumnProps } from 'yth-ui/es/components/list';
import style from './task.module.less';

const { TabPane } = Tabs;

/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗的类别 add 新增 view 查看 edit 编辑 */
  type: string;
  /** 弹窗传入的数据 */
  dataObj: { id?: string; [key: string]: React.Key };
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
};

/**
 * @description 查看 或新增 modal
 * @param PropsTypes PropsTypes
 */
const AddDialog: React.FC<PropsTypes> = ({ type, dataObj, closeModal = () => {} }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isCasRequested, setIsCasRequested] = useState<boolean>(false);
  const [taskDevicesList, setTaskDevicesList] = useState<TaskDeviceVo[]>([]);
  const [taskPointsList, setTaskPointsList] = useState<TaskPointsVo[]>([]);
  const [deviceModalVisible, setDeviceModalVisible] = useState<boolean>(false);
  const [pointModalVisible, setPointModalVisible] = useState<boolean>(false);
  const [currentDevice, setCurrentDevice] = useState<TaskDeviceVo>({});
  const [currentPoint, setCurrentPoint] = useState<TaskPointsVo>({});
  const [editingDeviceIndex, setEditingDeviceIndex] = useState<number>(-1);
  const [editingPointIndex, setEditingPointIndex] = useState<number>(-1);

  // 表单
  const form: ReturnType<typeof YTHForm.createForm> = React.useMemo(
    () => YTHForm.createForm({}),
    [],
  );

  const deviceForm: ReturnType<typeof YTHForm.createForm> = React.useMemo(
    () => YTHForm.createForm({}),
    [],
  );

  const pointForm: ReturnType<typeof YTHForm.createForm> = React.useMemo(
    () => YTHForm.createForm({}),
    [],
  );

  const { TextArea } = Input;

  // 查询详情
  const queryDataDetail: () => Promise<void> = async () => {
    setIsLoading(true);
    const res: BaseResponse<TaskVo> = await taskApi.getDetailById({
      id: dataObj.id,
    });
    if (res && res.code && res.code === 200) {
      const formD: TaskVo = res.data;
      form.setValues(formD);
      setTaskDevicesList(formD.taskDevicesList || []);
      setTaskPointsList(formD.taskPointsList || []);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (type && (type === 'edit' || type === 'view')) {
      queryDataDetail().then(() => {
        setIsCasRequested(true);
      });
    } else {
      setIsCasRequested(true);
    }
  }, [type, dataObj]);

  // 点击取消
  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  // 新增保存
  const submitAddData: (data: TaskInsertParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const submitData: TaskInsertParam = {
      ...data,
      taskDevicesList,
      taskPointsList,
    };
    const res: BaseResponse<object> = await taskApi.insert(submitData);
    if (res && res.code && res.code === 200) {
      message.success('新增数据成功');
      closeModal();
    } else {
      message.error('新增数据失败');
    }
    setIsLoading(false);
  };

  // 编辑保存
  const submitEditData: (data: TaskUpdateParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const submitData: TaskUpdateParam = {
      ...data,
      id: dataObj?.id,
      taskDevicesList,
      taskPointsList,
    };
    const res: BaseResponse<object> = await taskApi.update(submitData);
    if (res && res.code && res.code === 200) {
      message.success('更新数据成功');
      closeModal();
    } else {
      message.error('更新数据失败');
    }
    setIsLoading(false);
  };

  // 点击保存
  const save: () => Promise<void> = async () => {
    form.validate().then(() => {
      const submitData: TaskInsertParam = JSON.parse(JSON.stringify(form.values));
      if (type === 'add') {
        submitAddData(submitData);
      } else if (type === 'edit') {
        submitEditData(submitData);
      }
    });
  };

  // 设备相关操作
  const handleAddDevice: () => void = (): void => {
    setCurrentDevice({});
    setEditingDeviceIndex(-1);
    deviceForm.reset();
    setDeviceModalVisible(true);
  };

  const handleEditDevice: (record: TaskDeviceVo, index: number) => void = (record, index) => {
    setCurrentDevice(record);
    setEditingDeviceIndex(index);
    deviceForm.setValues(record);
    setDeviceModalVisible(true);
  };

  const handleDeleteDevice: (index: number) => void = (index) => {
    const newList: TaskDeviceVo[] = [...taskDevicesList];
    newList.splice(index, 1);
    setTaskDevicesList(newList);
  };

  const handleDeviceOk: () => void = () => {
    deviceForm.validate().then(() => {
      const deviceData: TaskDeviceVo = deviceForm.values;
      if (editingDeviceIndex >= 0) {
        // 编辑
        const newList: TaskDeviceVo[] = [...taskDevicesList];
        newList[editingDeviceIndex] = deviceData;
        setTaskDevicesList(newList);
      } else {
        // 新增
        setTaskDevicesList([...taskDevicesList, deviceData]);
      }
      setDeviceModalVisible(false);
    });
  };

  // 检查点相关操作
  const handleAddPoint: () => void = () => {
    setCurrentPoint({});
    setEditingPointIndex(-1);
    pointForm.reset();
    setPointModalVisible(true);
  };

  const handleEditPoint: (record: TaskPointsVo, index: number) => void = (
    record: TaskPointsVo,
    index: number,
  ) => {
    setCurrentPoint(record);
    setEditingPointIndex(index);
    pointForm.setValues(record);
    setPointModalVisible(true);
  };

  const handleDeletePoint: (index: number) => void = (index: number) => {
    const newList: TaskPointsVo[] = [...taskPointsList];
    newList.splice(index, 1);
    setTaskPointsList(newList);
  };

  const handlePointOk: () => void = () => {
    pointForm.validate().then(() => {
      const pointData: TaskPointsVo = pointForm.values;
      if (editingPointIndex >= 0) {
        // 编辑
        const newList: TaskPointsVo[] = [...taskPointsList];
        newList[editingPointIndex] = pointData;
        setTaskPointsList(newList);
      } else {
        // 新增
        setTaskPointsList([...taskPointsList, pointData]);
      }
      setPointModalVisible(false);
    });
  };

  // 设备表格列定义
  const deviceColumns: IYTHColumnProps[] = [
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      key: 'deviceType',
    },
    {
      title: '设备编码',
      dataIndex: 'deviceCode',
      key: 'deviceCode',
    },
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      key: 'deviceName',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: TaskDeviceVo, index: number) => (
        <Space size="middle">
          {type !== 'view' && (
            <>
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEditDevice(record, index)}
              >
                编辑
              </Button>
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteDevice(index)}
              >
                删除
              </Button>
            </>
          )}
        </Space>
      ),
      dataIndex: 'action',
    },
  ];

  // 检查点表格列定义
  const pointColumns: IYTHColumnProps[] = [
    {
      title: '检查点名称',
      dataIndex: 'pointName',
      key: 'pointName',
    },
    {
      title: '检查点坐标',
      dataIndex: 'pointLocation',
      key: 'pointLocation',
    },
    {
      title: '巡检内容',
      dataIndex: 'inspectionContent',
      key: 'inspectionContent',
    },
    {
      title: '检查状态',
      dataIndex: 'checkStatus',
      key: 'checkStatus',
      render: (status: number) => (status === 1 ? '已检查' : '未检查'),
    },
    {
      title: '检查结果',
      dataIndex: 'checkResult',
      key: 'checkResult',
      render: (result: number) => (result === 1 ? '异常' : '正常'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: TaskPointsVo, index: number) => (
        <Space size="middle">
          {type !== 'view' && (
            <>
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEditPoint(record, index)}
              >
                编辑
              </Button>
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeletePoint(index)}
              >
                删除
              </Button>
            </>
          )}
        </Space>
      ),
      dataIndex: '',
    },
  ];

  return (
    <div className={style['yth-inspection-moduel']}>
      <Spin spinning={isLoading}>
        {isCasRequested && (
          <>
            <YTHForm form={form} col={2}>
              <YTHForm.Item
                name="id"
                title="id"
                labelType={2}
                required={false}
                display="hidden"
                componentName="Input"
                componentProps={{
                  disabled: true,
                }}
              />

              <YTHForm.Item
                name="taskCode"
                title="任务编码"
                labelType={2}
                required
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入任务编码',
                }}
              />

              <YTHForm.Item
                name="taskName"
                title="任务名称"
                labelType={2}
                required
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入任务名称',
                }}
              />

              <YTHForm.Item
                name="TaskId"
                title="计划ID"
                labelType={2}
                required={false}
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入计划ID',
                }}
              />

              <YTHForm.Item
                name="TaskName"
                title="计划名称"
                labelType={2}
                required={false}
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入计划名称',
                }}
              />

              <YTHForm.Item
                name="inspectionMethod"
                title="巡检方式"
                labelType={2}
                required
                componentName="Selector"
                componentProps={{
                  request: async () => {
                    return baseApi.getDictionary(dicParams.INSPECTION_METHOD);
                  },
                  disabled: type === 'view',
                  p_props: {
                    placeholder: '请选择巡检方式',
                  },
                }}
              />

              <YTHForm.Item
                name="taskType"
                title="任务类型"
                labelType={2}
                required
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入任务类型',
                }}
              />

              <YTHForm.Item
                name="directorUserId"
                title="负责人ID"
                labelType={2}
                required
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入负责人ID',
                }}
              />

              <YTHForm.Item
                name="directorUserName"
                title="负责人名称"
                labelType={2}
                required
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入负责人名称',
                }}
              />

              <YTHForm.Item
                name="directorPhone"
                title="负责人电话"
                labelType={2}
                validator={validateMobileOrLinePhone}
                required
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入负责人电话',
                }}
              />

              <YTHForm.Item
                name="taskStatus"
                title="任务状态"
                labelType={2}
                required
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入任务状态',
                }}
              />

              <YTHForm.Item
                required
                labelType={2}
                title="计划开始时间"
                name="TaskStartTime"
                componentName="DatePicker"
                componentProps={{
                  placeholder: '请选择计划开始时间',
                  precision: 'day',
                  formatter: 'YYYY-MM-DD HH:mm:ss',
                  disabled: type === 'view',
                }}
              />

              <YTHForm.Item
                required
                labelType={2}
                title="计划结束时间"
                name="TaskEndTime"
                componentName="DatePicker"
                componentProps={{
                  placeholder: '请选择计划结束时间',
                  precision: 'day',
                  formatter: 'YYYY-MM-DD HH:mm:ss',
                  disabled: type === 'view',
                }}
              />

              <YTHForm.Item
                labelType={2}
                title="实际开始时间"
                name="actualStartTime"
                componentName="DatePicker"
                componentProps={{
                  placeholder: '请选择实际开始时间',
                  precision: 'day',
                  formatter: 'YYYY-MM-DD HH:mm:ss',
                  disabled: type === 'view',
                }}
              />

              <YTHForm.Item
                labelType={2}
                title="实际结束时间"
                name="actualEndTime"
                componentName="DatePicker"
                componentProps={{
                  placeholder: '请选择实际结束时间',
                  precision: 'day',
                  formatter: 'YYYY-MM-DD HH:mm:ss',
                  disabled: type === 'view',
                }}
              />
              <YTHForm.Item
                name="partyUserIds"
                title="参与巡检人"
                labelType={2}
                componentName="Input"
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入参与巡检人ID，多个用逗号分隔',
                }}
                mergeRow={2}
              />

              <YTHForm.Item
                name="remark"
                title="备注"
                labelType={2}
                component={TextArea}
                componentProps={{
                  disabled: type === 'view',
                  placeholder: '请输入备注信息',
                  rows: 3,
                }}
                mergeRow={2}
              />

              <YTHForm.Item
                name="isRemind"
                title="是否发送短信提醒"
                labelType={2}
                componentName="Selector"
                componentProps={{
                  request: async () => {
                    return [
                      { code: '0', text: '不发送' },
                      { code: '1', text: '发送' },
                    ];
                  },
                  disabled: type === 'view',
                  p_props: {
                    placeholder: '请选择是否发送短信提醒',
                  },
                }}
              />
            </YTHForm>

            <div style={{ marginTop: 20 }}>
              <Tabs defaultActiveKey="1">
                <TabPane tab="任务设备" key="1">
                  <div style={{ marginBottom: 16 }}>
                    {type !== 'view' && (
                      <Button type="primary" icon={<PlusOutlined />} onClick={handleAddDevice}>
                        添加设备
                      </Button>
                    )}
                  </div>
                  <Table
                    columns={deviceColumns}
                    dataSource={taskDevicesList}
                    rowKey={(record, index) => index?.toString() || '0'}
                    pagination={false}
                    size="small"
                  />
                </TabPane>
                <TabPane tab="任务检查点" key="2">
                  <div style={{ marginBottom: 16 }}>
                    {type !== 'view' && (
                      <Button type="primary" icon={<PlusOutlined />} onClick={handleAddPoint}>
                        添加检查点
                      </Button>
                    )}
                  </div>
                  <Table
                    columns={pointColumns}
                    dataSource={taskPointsList}
                    rowKey={(record, index) => index?.toString() || '0'}
                    pagination={false}
                    size="small"
                  />
                </TabPane>
              </Tabs>
            </div>
          </>
        )}
        <div className={style['drawer-filter-operation']}>
          {(type === 'add' || type === 'edit') && (
            <Button onClick={save} className={style['search-btn']} type="primary">
              保存
            </Button>
          )}
          <Button onClick={cancel} className={style['reset-btn']}>
            取消
          </Button>
        </div>
      </Spin>

      {/* 设备编辑弹窗 */}
      <Modal
        title={editingDeviceIndex >= 0 ? '编辑设备' : '添加设备'}
        visible={deviceModalVisible}
        onOk={handleDeviceOk}
        onCancel={() => setDeviceModalVisible(false)}
        destroyOnClose
      >
        <YTHForm form={deviceForm} col={1}>
          <YTHForm.Item
            name="deviceType"
            title="设备类型"
            labelType={2}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入设备类型',
            }}
          />
          <YTHForm.Item
            name="deviceId"
            title="设备ID"
            labelType={2}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入设备ID',
            }}
          />
          <YTHForm.Item
            name="deviceCode"
            title="设备编码"
            labelType={2}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入设备编码',
            }}
          />
          <YTHForm.Item
            name="deviceName"
            title="设备名称"
            labelType={2}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入设备名称',
            }}
          />
          <YTHForm.Item
            name="remark"
            title="备注"
            labelType={2}
            component={TextArea}
            componentProps={{
              placeholder: '请输入备注信息',
              rows: 3,
            }}
          />
        </YTHForm>
      </Modal>

      {/* 检查点编辑弹窗 */}
      <Modal
        title={editingPointIndex >= 0 ? '编辑检查点' : '添加检查点'}
        visible={pointModalVisible}
        onOk={handlePointOk}
        onCancel={() => setPointModalVisible(false)}
        destroyOnClose
      >
        <YTHForm form={pointForm} col={1}>
          <YTHForm.Item
            name="pointName"
            title="检查点名称"
            labelType={2}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入检查点名称',
            }}
          />
          <YTHForm.Item
            name="pointLocation"
            title="检查点坐标"
            labelType={2}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入检查点坐标',
            }}
          />
          <YTHForm.Item
            name="inspectionContent"
            title="巡检内容"
            labelType={2}
            required
            component={TextArea}
            componentProps={{
              placeholder: '请输入巡检内容',
              rows: 3,
            }}
          />
          <YTHForm.Item
            name="checkStatus"
            title="检查状态"
            labelType={2}
            componentName="Selector"
            componentProps={{
              request: async () => {
                return [
                  { code: '0', text: '未检查' },
                  { code: '1', text: '已检查' },
                ];
              },
              p_props: {
                placeholder: '请选择检查状态',
              },
            }}
          />
          <YTHForm.Item
            name="checkResult"
            title="检查结果"
            labelType={2}
            componentName="Selector"
            componentProps={{
              request: async () => {
                return [
                  { code: '0', text: '正常' },
                  { code: '1', text: '异常' },
                ];
              },
              p_props: {
                placeholder: '请选择检查结果',
              },
            }}
          />
          <YTHForm.Item
            name="checkResultDescription"
            title="结果描述"
            labelType={2}
            component={TextArea}
            componentProps={{
              placeholder: '请输入结果描述',
              rows: 3,
            }}
          />
          <YTHForm.Item
            name="attachments"
            title="结果照片"
            labelType={2}
            componentName="Upload"
            componentProps={{
              listType: `yth-card`,
              name: 'file',
              action: '/gw/form-api/file/upload',
              headers: {
                authorization: Token(),
              },
              online: '/preview/onlinePreview',
              data: {
                formCode: 'task_attachments',
              },
            }}
          />
        </YTHForm>
      </Modal>
    </div>
  );
};

export default AddDialog;
